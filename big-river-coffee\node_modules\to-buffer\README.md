# to-buffer <sup>[![Version Badge][2]][1]</sup>

Pass in a string, array, Buffer, Data View, or Uint8Array, and get a Buffer back.

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![dependency status][5]][6]
[![dev dependency status][7]][8]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][11]][1]


```
npm install to-buffer
```

## Usage

``` js
var toBuffer = require('to-buffer');

console.log(toBuffer('hi')); // <Buffer 68 69>
console.log(toBuffer(Buffer('hi'))); // <Buffer 68 69>
console.log(toBuffer('6869', 'hex')); // <Buffer 68 69>
console.log(toBuffer(43)); // throws
```

[1]: https://npmjs.org/package/to-buffer
[2]: https://versionbadg.es/browserify/to-buffer.svg
[5]: https://david-dm.org/browserify/to-buffer.svg
[6]: https://david-dm.org/browserify/to-buffer
[7]: https://david-dm.org/browserify/to-buffer/dev-status.svg
[8]: https://david-dm.org/browserify/to-buffer#info=devDependencies
[11]: https://nodei.co/npm/to-buffer.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/to-buffer.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/to-buffer.svg
[downloads-url]: https://npm-stat.com/charts.html?package=to-buffer
[codecov-image]: https://codecov.io/gh/browserify/to-buffer/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/browserify/to-buffer/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/browserify/to-buffer
[actions-url]: https://github.com/browserify/to-buffer/actions
