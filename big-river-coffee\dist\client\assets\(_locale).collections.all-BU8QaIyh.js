import{w as Ue}from"./with-props-CDGKRMwV.js";import{j as e}from"./jsx-runtime-6BLhrb1x.js";import{u as Qe,b as Je,c as Ze,r as c,d as Xe}from"./chunk-D4RADZKF-Oib9MYUd.js";import{P as et}from"./ProductCard-BAsJcZLb.js";import{A as ze}from"./AddToCartButton-BXvoJOf_.js";import{u as tt}from"./Aside-Nw97VOZz.js";import{i as st}from"./scrollAnimations-D2F8dg76.js";import{M as rt}from"./Money-DCyB3BQp.js";import{a as at}from"./getProductOptions-DiFA5z_h.js";import"./variants-NDcN9fzL.js";import"./Image-CLBTrHWZ.js";import"./index-DFyX0ANY.js";const St=()=>[{title:"All Coffees | Big River Coffee"},{description:"Discover our complete collection of premium coffee - ethically sourced and expertly roasted for every adventure"}],Lt=Ue(function(){const{products:Y}=Qe(),[N]=Je(),Ee=Ze(),[nt,it]=c.useState(Y.nodes),[_,be]=c.useState("grid"),[ve,He]=c.useState("featured"),[lt,ct]=c.useState(!1),[dt,ut]=c.useState(!0),[j,U]=c.useState("coffee"),[Q,J]=c.useState(!1),[mt,xt]=c.useState(!1),Z=c.useRef(null),X=c.useRef(null),ee=c.useRef(null),te=c.useRef(null),ye=N.get("roast")||"all",je=N.get("kcup-type")||"all",we=N.get("gear-category")||"all",Ne=N.get("price")||"all",ke=N.get("section")||"coffee",ce=t=>{const a=t.title.toLowerCase(),i=t.tags||[];return a.includes("k-cup")||a.includes("kcup")||a.includes("k cup")||i.some(o=>o.toLowerCase().includes("k-cup"))?"kcups":a.includes("mug")||a.includes("cup")||a.includes("tumbler")||a.includes("grinder")||a.includes("equipment")||a.includes("gear")||a.includes("shirt")||a.includes("hat")||a.includes("apparel")||i.some(o=>{const r=o.toLowerCase();return r.includes("gear")||r.includes("equipment")||r.includes("apparel")||r.includes("accessories")})?"gear":"coffee"},H={coffee:Y.nodes.filter(t=>ce(t)==="coffee"),kcups:Y.nodes.filter(t=>ce(t)==="kcups"),gear:Y.nodes.filter(t=>ce(t)==="gear")},qe=(t,a)=>{let i=[...a];switch(t==="coffee"&&ye!=="all"?i=i.filter(o=>o.title.toLowerCase().includes(ye)):t==="kcups"&&je!=="all"?i=i.filter(o=>{const r=o.title.toLowerCase();switch(je){case"single-origin":return r.includes("single")||r.includes("origin");case"blend":return r.includes("blend");case"flavored":return r.includes("vanilla")||r.includes("caramel")||r.includes("hazelnut")||r.includes("flavored");default:return!0}}):t==="gear"&&we!=="all"&&(i=i.filter(o=>{const r=o.title.toLowerCase();switch(we){case"mugs":return r.includes("mug")||r.includes("cup")||r.includes("tumbler");case"equipment":return r.includes("grinder")||r.includes("equipment")||r.includes("machine");case"apparel":return r.includes("shirt")||r.includes("hat")||r.includes("apparel");case"accessories":return r.includes("accessory")||r.includes("gear")||!r.includes("mug")&&!r.includes("cup")&&!r.includes("grinder")&&!r.includes("shirt");default:return!0}})),Ne!=="all"&&(i=i.filter(o=>{const r=parseFloat(o.priceRange.minVariantPrice.amount);switch(Ne){case"under-25":return r<25;case"25-50":return r>=25&&r<=50;case"over-50":return r>50;default:return!0}})),ve){case"price-low":i.sort((o,r)=>parseFloat(o.priceRange.minVariantPrice.amount)-parseFloat(r.priceRange.minVariantPrice.amount));break;case"price-high":i.sort((o,r)=>parseFloat(r.priceRange.minVariantPrice.amount)-parseFloat(o.priceRange.minVariantPrice.amount));break;case"name":i.sort((o,r)=>o.title.localeCompare(r.title));break}return i};c.useEffect(()=>{Q||U(ke)},[ke,Q]),c.useEffect(()=>{const t=N.get("section");t&&["coffee","kcups","subscriptions","gear"].includes(t)&&(U(t),setTimeout(()=>{const a=t==="coffee"?Z:t==="kcups"?X:t==="subscriptions"?ee:te;if(a.current){const r=a.current.offsetTop-180;window.scrollTo({top:r,behavior:"smooth"})}},300))},[N]),c.useEffect(()=>{let t,a=!1;const i=()=>{Q||a||(a=!0,clearTimeout(t),t=setTimeout(()=>{try{const o=[{name:"coffee",ref:Z},{name:"kcups",ref:X},{name:"subscriptions",ref:ee},{name:"gear",ref:te}],r=window.pageYOffset,S=180;let L=j;for(const P of o)if(P.ref.current){const W=P.ref.current.offsetTop-S,se=W+P.ref.current.offsetHeight;if(r>=W&&r<se){L=P.name;break}}if(L!==j){U(L);const P=new URLSearchParams(N);P.set("section",L),window.history.replaceState({},"",`?${P.toString()}`)}}catch(o){console.error("Scroll handling error:",o)}finally{a=!1}},150))};return window.addEventListener("scroll",i,{passive:!0}),()=>{window.removeEventListener("scroll",i),clearTimeout(t)}},[j,N,Q]),c.useEffect(()=>{const t=()=>{const r=document.querySelector(".collections-mobile-nav");r&&(r.style.display=window.innerWidth<1024?"block":"none",r.style.visibility="visible");const S=document.querySelector(".collections-desktop-nav");S&&(S.style.display=window.innerWidth>=1024?"flex":"none",S.style.visibility="visible")},a=setTimeout(t,10),i=setTimeout(t,100),o=setTimeout(t,500);return window.addEventListener("resize",t),document.addEventListener("visibilitychange",t),()=>{clearTimeout(a),clearTimeout(i),clearTimeout(o),window.removeEventListener("resize",t),document.removeEventListener("visibilitychange",t)}},[]);const Ie=t=>{He(t)},Ce=t=>{try{J(!0),U(t);const i={coffee:Z,kcups:X,subscriptions:ee,gear:te}[t];if(i.current){const r=i.current.offsetTop,S=Math.max(0,r-200),L=new URLSearchParams(N);L.set("section",t),window.history.replaceState({},"",`?${L.toString()}`),window.scrollTo({top:S,behavior:"smooth"}),setTimeout(()=>{J(!1)},1e3)}else console.warn(`Section ref not found for: ${t}`),J(!1)}catch(a){console.error("Error in scrollToSection:",a),J(!1)}},de=t=>{const a={coffee:H.coffee.length,kcups:H.kcups.length,subscriptions:1,gear:H.gear.length};return{coffee:{title:"Coffee",description:"Premium coffee beans, ethically sourced and expertly roasted",icon:e.jsxs("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{d:"M2 21h18v-2H2v2zM20 8h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v8c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.1 0 2-.9 2-2s-.9-2-2-2zM16 13c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V5h12v8z"}),e.jsx("path",{d:"M20 10c.55 0 1-.45 1-1s-.45-1-1-1v2z"})]}),count:a.coffee},kcups:{title:"K-Cups",description:"Convenient single-serve coffee pods for your Keurig",icon:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),count:a.kcups},gear:{title:"Gear",description:"Coffee equipment, mugs, and adventure gear",icon:e.jsxs("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),count:a.gear},subscriptions:{title:"Subscriptions",description:"Never run out of coffee with our flexible subscription service",icon:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),count:a.subscriptions}}[t]};return c.useEffect(()=>{if(!(typeof window>"u"))return document.body.classList.add("collections-page"),()=>{document.body.classList.remove("collections-page")}},[]),c.useEffect(()=>st(),[]),e.jsxs(e.Fragment,{children:[e.jsx("style",{children:`
        body.collections-page {
          margin: 0 !important;
          padding: 0 !important;
        }
        body.collections-page main {
          padding: 0 !important;
          margin: 0 !important;
        }
      `}),e.jsx("div",{className:"hidden sm:block sticky top-0 w-full h-screen z-0",children:e.jsx("div",{className:"w-full h-full",children:e.jsx("video",{src:"/newhomepage/shop_hero_vid.mp4",autoPlay:!0,muted:!0,loop:!0,playsInline:!0,className:"w-full h-full object-cover",style:{width:"100%",height:"100%",display:"block"}})})}),e.jsxs("div",{className:"relative z-10 min-h-screen bg-gray-50",style:{marginTop:"0",paddingTop:"0"},children:[e.jsx("div",{className:"sticky z-40 bg-white border-b border-neutral-200 shadow-sm top-0",children:e.jsx("div",{className:"container-clean",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between py-4 gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"hidden lg:flex space-x-2",children:["coffee","kcups","subscriptions","gear"].map(t=>{const a=de(t),i=j===t;return e.jsxs("button",{type:"button",onClick:o=>{o.preventDefault(),o.stopPropagation(),Ce(t)},className:`relative flex items-center px-4 py-2 rounded-lg font-semibold transition-all duration-300 whitespace-nowrap ${i?"bg-amber-500 text-white shadow-lg":"text-neutral-600 hover:text-amber-500 hover:bg-amber-50"}`,children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:a.icon}),e.jsx("span",{className:"mr-3",children:a.title}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full font-medium flex-shrink-0 ${i?"bg-white/20 text-white":"bg-army-100 text-army-800"}`,children:a.count})]},`desktop-${t}`)})}),e.jsx("div",{className:"lg:hidden",children:e.jsx("div",{className:"grid grid-cols-2 gap-3",children:["coffee","kcups","subscriptions","gear"].map(t=>{const a=de(t),i=j===t;return e.jsxs("button",{type:"button",onClick:o=>{o.preventDefault(),o.stopPropagation(),Ce(t)},className:`flex items-center justify-center px-3 py-3 rounded-lg font-medium transition-all duration-200 ${i?"bg-amber-500 text-white shadow-lg":"bg-white text-neutral-700 border border-neutral-200 hover:border-amber-300 hover:text-amber-600"}`,children:[e.jsx("span",{className:"mr-2 flex-shrink-0",children:a.icon}),e.jsx("span",{className:"text-sm font-semibold",children:a.title})]},`mobile-${t}`)})})})]}),e.jsxs("div",{className:"hidden lg:flex items-center space-x-4",children:[e.jsxs("select",{value:ve,onChange:t=>Ie(t.target.value),className:"border border-neutral-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-army-500 min-w-[140px]",children:[e.jsx("option",{value:"featured",children:"Featured"}),e.jsx("option",{value:"name",children:"Name A-Z"}),e.jsx("option",{value:"price-low",children:"Price: Low to High"}),e.jsx("option",{value:"price-high",children:"Price: High to Low"})]}),e.jsxs("div",{className:"flex items-center border border-neutral-300 rounded-lg overflow-hidden bg-white shadow-sm",children:[e.jsx("button",{onClick:()=>be("grid"),className:`relative flex items-center justify-center w-12 h-12 transition-all duration-200 ${_==="grid"?"bg-amber-500 text-white shadow-sm":"bg-white text-neutral-600 hover:bg-neutral-50 hover:text-amber-500"}`,title:"Grid View","aria-label":"Grid View",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})})}),e.jsx("div",{className:"w-px h-6 bg-neutral-300"}),e.jsx("button",{onClick:()=>be("list"),className:`relative flex items-center justify-center w-12 h-12 transition-all duration-200 ${_==="list"?"bg-amber-500 text-white shadow-sm":"bg-white text-neutral-600 hover:bg-neutral-50 hover:text-amber-500"}`,title:"List View","aria-label":"List View",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})})})]})]})]})})}),e.jsxs("div",{className:"container-clean py-12",children:[e.jsx("div",{className:"flex flex-col lg:flex-row gap-8",children:e.jsx("div",{className:"flex-1",children:["coffee","kcups","subscriptions","gear"].map(t=>{var r,S,L,P,W,se,Se;const a=de(t),i=t==="subscriptions"?[]:H[t],o=t==="subscriptions"?[]:qe(t,i);if(t==="kcups"){const d=o[0];if(d&&o.length>0){const k=((r=d.variants)==null?void 0:r.nodes)||[],v=k.filter(x=>x.availableForSale),A=v[0]||k[0];if(!A)return null;const[m,R]=c.useState(A),[h,B]=c.useState(1),O=x=>{var q;if(x.title&&x.title!=="Default Title")return x.title;const p=(q=x.selectedOptions)==null?void 0:q.find(y=>y.name.toLowerCase().includes("flavor")||y.name.toLowerCase().includes("title")||y.name.toLowerCase().includes("variant")||y.name.toLowerCase().includes("type"));return p!=null&&p.value?p.value:x.title||`K-Cup Flavor ${x.id.split("/").pop()}`};return e.jsxs("div",{ref:X,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"140px"},children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600",children:a.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-3xl font-bold transition-colors duration-200 ${j===t?"text-amber-600":"text-gray-900"}`,children:a.title}),e.jsx("p",{className:"text-gray-600",children:a.description})]})]})}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${j===t?"bg-amber-600 w-24":"bg-gray-200 w-16"}`})]}),e.jsx("div",{className:"bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-4 sm:p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[e.jsx("div",{className:"kcup-image-container aspect-square bg-white rounded-xl overflow-hidden shadow-sm max-w-sm mx-auto lg:max-w-md lg:mx-0",children:d.featuredImage?e.jsx("img",{src:d.featuredImage.url,alt:d.featuredImage.altText||d.title,className:"w-full h-full object-contain p-4"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsx("svg",{className:"w-16 h-16 sm:w-24 sm:h-24 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})})}),e.jsxs("div",{className:"flex flex-col justify-between space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-2",children:"Big River K-Cups"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600 mb-4",children:"20 count single-serve pods • Compatible with Keurig 1.0 & 2.0"}),e.jsx("div",{className:"text-2xl sm:text-3xl font-bold text-army-700 mb-4 sm:mb-6",children:e.jsx(rt,{data:d.priceRange.minVariantPrice})}),e.jsxs("div",{className:"mb-4 sm:mb-6",children:[e.jsx("label",{className:"text-base sm:text-lg font-semibold text-gray-900 mb-3 block",children:"Flavors"}),v.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3",children:v.map(x=>{const p=O(x);return e.jsx("button",{onClick:()=>R(x),className:`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 text-left min-h-[48px] ${(m==null?void 0:m.id)===x.id?"border-amber-600 bg-amber-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600"}`,children:p},x.id)})}):k.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3",children:k.map(x=>{const p=O(x);return e.jsxs("button",{onClick:()=>R(x),disabled:!x.availableForSale,className:`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 text-left min-h-[48px] ${(m==null?void 0:m.id)===x.id?"border-amber-600 bg-amber-600 text-white shadow-md":x.availableForSale?"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600":"border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"}`,children:[p," ",!x.availableForSale&&"(Out of Stock)"]},x.id)})}):e.jsx("div",{className:"text-gray-500 text-sm",children:"No flavor options available"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between sm:justify-start sm:space-x-4",children:[e.jsx("span",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Quantity:"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>B(Math.max(1,h-1)),className:"w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors min-h-[44px] min-w-[44px]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),e.jsx("span",{className:"text-lg sm:text-xl font-semibold text-gray-900 min-w-[3rem] text-center",children:h}),e.jsx("button",{onClick:()=>B(h+1),className:"w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors min-h-[44px] min-w-[44px]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]})]}),e.jsx(ze,{disabled:!m||!m.availableForSale||!m.id,lines:m!=null&&m.id?[{merchandiseId:m.id,quantity:h}]:[],className:"w-full bg-amber-600 hover:bg-amber-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-semibold text-base sm:text-lg transition-colors duration-200 min-h-[48px]",children:m?m.availableForSale?"Add to cart":"Out of stock":"Select a flavor"})]})]})]})})]},t)}}if(t==="subscriptions"){const[d,k]=c.useState(""),[v,A]=c.useState(""),[m,R]=c.useState(""),[h,B]=c.useState(""),[O,x]=c.useState(""),[p,q]=c.useState(""),[y,ue]=c.useState(""),[M,me]=c.useState(""),[re,Le]=c.useState(1),[ae,We]=c.useState("monthly"),[xe,he]=c.useState(!1),fe=c.useRef(null),u=Xe(),{open:Ae}=tt();c.useEffect(()=>{const s=f=>{fe.current&&!fe.current.contains(f.target)&&he(!1)};return document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[]);const De=()=>H.coffee||[],Ge=(s,f=[])=>{const b=new FormData;b.append("action","fetchProduct"),b.append("handle",s),b.append("selectedOptions",JSON.stringify(f)),u.submit(b,{method:"POST"})},Ke=()=>{var s;return((s=H.kcups)==null?void 0:s[0])||null},Pe=s=>{var f,b;if(!s)return[];try{return at({...s,selectedOrFirstAvailableVariant:s.selectedOrFirstAvailableVariant||((b=(f=s.variants)==null?void 0:f.nodes)==null?void 0:b[0])})}catch(C){return console.error("Error getting product options:",C),[]}},Ye=(s,f)=>{var b;return(b=s==null?void 0:s.variants)!=null&&b.nodes?s.variants.nodes.find(C=>Object.entries(f).every(([V,w])=>{var $;return($=C.selectedOptions)==null?void 0:$.some(l=>l.name.toLowerCase()===V.toLowerCase()&&l.value===w)})):null},Fe=s=>{k(s),A(""),R(""),B(""),x(""),q(""),ue(""),me("")},Be=De(),oe=Ke(),D=Be.find(s=>s.id===v);c.useEffect(()=>{v&&D&&(R(""),B(""),Ge(D.handle,[]))},[v]);const ne=[{value:"weekly",label:"1 Week",sellingPlanId:"gid://shopify/SellingPlan/9581953339"},{value:"monthly",label:"1 Month",sellingPlanId:"gid://shopify/SellingPlan/9581986107"},{value:"3weeks",label:"3 Weeks",sellingPlanId:"gid://shopify/SellingPlan/9582018875"},{value:"6weeks",label:"6 Weeks",sellingPlanId:"gid://shopify/SellingPlan/9582051643"}],_e=ne.find(s=>s.value===ae)||ne[1];return e.jsxs("div",{ref:ee,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"140px"},children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600",children:a.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-3xl font-bold transition-colors duration-200 ${j===t?"text-amber-600":"text-gray-900"}`,children:"Big River Coffee Subscriptions"}),e.jsx("p",{className:"text-gray-600",children:"Never run out of coffee with our flexible subscription service"})]})]})}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${j===t?"bg-amber-600 w-24":"bg-gray-200 w-16"}`})]}),e.jsx("div",{className:"bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsx("div",{className:"aspect-square bg-white rounded-xl overflow-hidden shadow-sm",children:e.jsx("img",{src:"/subscriptionimg.webp",alt:"Big River Coffee Subscription",className:"w-full h-full object-center object-cover"})}),e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"Big River Coffee Subscription"}),e.jsx("span",{className:"bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold",children:"15% OFF"})]}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Fresh coffee delivered to your doorstep • Save 15% on every order • Free shipping on orders over $30"}),e.jsxs("div",{className:"mb-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Save your money with up to 15% off"]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Delay, modify & cancel anytime"]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Simplify your month with automatic delivery"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Choose Your Product"}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsx("button",{onClick:()=>Fe("coffee"),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${d==="coffee"?"border-army-600 bg-army-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600"}`,children:"Coffee Beans"}),e.jsx("button",{onClick:()=>Fe("kcups"),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${d==="kcups"?"border-amber-600 bg-amber-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600"}`,children:"K-Cups"})]})]}),d==="coffee"&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Select Coffee Product"}),e.jsx("div",{className:"relative",children:e.jsxs("select",{value:v,onChange:s=>{A(s.target.value),R(""),B(""),q(""),ue(""),me("")},className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose a coffee..."}),Be.map(s=>e.jsx("option",{value:s.id,children:s.title},s.id))]})})]}),d==="coffee"&&v&&e.jsx("div",{className:"mb-6 transition-all duration-200",children:u.state==="submitting"||u.state==="loading"?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Loading product options..."})]}):(S=u.data)!=null&&S.product?e.jsx("div",{className:"space-y-4",children:(()=>{var $;const s=Pe(u.data.product),f=u.data.product.id==="gid://shopify/Product/8965076156731"||u.data.product.title.toLowerCase().includes("build your own"),b=u.data.product.id==="gid://shopify/Product/10111589941563"||u.data.product.title.toLowerCase().includes("blend box"),C=u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box");if(f||s.some(l=>l.name.includes("Flavor #"))){const l=s.find(n=>n.name==="Flavor #1"),g=(($=l==null?void 0:l.optionValues)==null?void 0:$.map(n=>n.name))||[];return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Build Your Own Bundle"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Pick 3 flavors for your custom coffee bundle"})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #1"}),e.jsxs("select",{value:p,onChange:n=>q(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm",children:[e.jsx("option",{value:"",children:"Choose..."}),g.map(n=>e.jsx("option",{value:n,children:n},n))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #2"}),e.jsxs("select",{value:y,onChange:n=>ue(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm",children:[e.jsx("option",{value:"",children:"Choose..."}),g.map(n=>e.jsx("option",{value:n,children:n},n))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #3"}),e.jsxs("select",{value:M,onChange:n=>me(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm",children:[e.jsx("option",{value:"",children:"Choose..."}),g.map(n=>e.jsx("option",{value:n,children:n},n))]})]})]}),p&&y&&M&&e.jsx("div",{className:"bg-army-50 border border-army-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm font-medium text-army-800",children:["Your Bundle: ",p," + ",y," + ",M]})})]})}if(b){const l=s.find(n=>n.name==="Box Selection"),g=s.find(n=>n.name==="Type");return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Blend Box Selection"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Choose your blend combination"})]}),l&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Blend Combination"}),e.jsxs("select",{value:m,onChange:n=>R(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose combination..."}),l.optionValues.map(n=>e.jsx("option",{value:n.name,children:n.name},n.name))]})]}),g&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Grind Type"}),e.jsxs("select",{value:h,onChange:n=>B(n.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose grind type..."}),g.optionValues.map(n=>e.jsx("option",{value:n.name,children:n.name},n.name))]})]})]})}if(C){const l=s.find(g=>g.name==="Type"||g.name==="Grind"||g.name==="Grind Type"||g.name.toLowerCase().includes("type")||g.name.toLowerCase().includes("grind"));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Roasters Box Selection"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Select your grind preference"})]}),l?e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:l.name}),e.jsxs("select",{value:h,onChange:g=>B(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsxs("option",{value:"",children:["Choose ",l.name.toLowerCase(),"..."]}),l.optionValues.map(g=>e.jsx("option",{value:g.name,children:g.name},g.name))]})]}):e.jsx("div",{className:"text-gray-500 text-sm",children:"No variant options available for this product."})]})}const V=s.find(l=>l.name==="Size"),w=s.find(l=>l.name==="type"||l.name==="Type");return e.jsxs(e.Fragment,{children:[V&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Size"}),e.jsxs("select",{value:m,onChange:l=>R(l.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose size..."}),V.optionValues.map(l=>e.jsx("option",{value:l.name,children:l.name},l.name))]})]}),w&&w.optionValues&&w.optionValues.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:w.name==="type"?"Type":w.name}),e.jsxs("select",{value:h,onChange:l=>B(l.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsxs("option",{value:"",children:["Choose ",w.name.toLowerCase(),"..."]}),w.optionValues.map(l=>e.jsx("option",{value:l.name,children:l.name},l.name))]})]})]})})()}):(L=u.data)!=null&&L.error?e.jsxs("div",{className:"text-center py-4 text-red-500",children:["Error loading product options: ",u.data.error]}):e.jsx("div",{className:"text-center py-4 text-gray-500",children:"Select a coffee product to see available options"})}),d==="kcups"&&oe&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Select Flavor"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:(W=(P=oe.variants)==null?void 0:P.nodes)==null?void 0:W.map(s=>{var f,b;return e.jsx("button",{onClick:()=>x(s.id),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${O===s.id?"border-amber-600 bg-amber-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600"}`,children:((b=(f=s.selectedOptions)==null?void 0:f.find(C=>C.name.toLowerCase().includes("title")||C.name.toLowerCase().includes("flavor")))==null?void 0:b.value)||s.title||"K-Cup Variant"},s.id)})})]}),d&&(d==="coffee"&&v&&(m&&h||p&&y&&M||((se=u.data)==null?void 0:se.product)&&(u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box"))&&h)||d==="kcups"&&O)&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Delivery Frequency"}),e.jsxs("div",{className:"relative",ref:fe,children:[e.jsx("button",{type:"button",onClick:()=>he(!xe),className:"w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 hover:border-gray-400 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium text-gray-900",children:_e.label}),e.jsx("svg",{className:`w-5 h-5 text-gray-400 transition-transform duration-200 ${xe?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),xe&&e.jsx("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:ne.map(s=>e.jsx("button",{type:"button",onClick:()=>{We(s.value),he(!1)},className:`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg ${ae===s.value?"bg-army-50 border-l-4 border-army-500":""}`,children:e.jsx("span",{className:`font-medium ${ae===s.value?"text-army-900":"text-gray-900"}`,children:s.label})},s.value))})]})]})]}),d&&(d==="coffee"&&v&&(m&&h||p&&y&&M||((Se=u.data)==null?void 0:Se.product)&&(u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box"))&&h)||d==="kcups"&&O)&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Quantity:"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>Le(Math.max(1,re-1)),className:"w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),e.jsx("span",{className:"text-xl font-semibold text-gray-900 min-w-[3rem] text-center",children:re}),e.jsx("button",{onClick:()=>Le(re+1),className:"w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]})]}),e.jsx("div",{className:"space-y-3",children:(()=>{var b,C,V,w,$,l,g,n,Me,Te,Re,Oe,$e;const s=ne.find(z=>z.value===ae);let f=null;if(d==="coffee"&&v&&((b=u.data)!=null&&b.product)){const z=u.data.product.id==="gid://shopify/Product/8965076156731"||Pe(u.data.product).some(T=>T.name.includes("Flavor #")),ge=u.data.product.id==="gid://shopify/Product/10111589941563",pe=u.data.product.id==="gid://shopify/Product/10111587647803";z&&p&&y&&M?f=(V=(C=u.data.product.variants)==null?void 0:C.nodes)==null?void 0:V.find(T=>{var G,I,Ve;const E=T.selectedOptions||[],ie=((G=E.find(K=>K.name==="Flavor #1"))==null?void 0:G.value)===p,F=((I=E.find(K=>K.name==="Flavor #2"))==null?void 0:I.value)===y,le=((Ve=E.find(K=>K.name==="Flavor #3"))==null?void 0:Ve.value)===M;return ie&&F&&le}):ge&&m&&h?f=($=(w=u.data.product.variants)==null?void 0:w.nodes)==null?void 0:$.find(T=>{var le,G;const E=T.selectedOptions||[],ie=((le=E.find(I=>I.name==="Box Selection"))==null?void 0:le.value)===m,F=((G=E.find(I=>I.name==="Type"))==null?void 0:G.value)===h;return ie&&F}):pe&&h?f=(g=(l=u.data.product.variants)==null?void 0:l.nodes)==null?void 0:g.find(T=>(T.selectedOptions||[]).find(F=>(F.name==="Type"||F.name==="Grind"||F.name==="Grind Type"||F.name.toLowerCase().includes("type")||F.name.toLowerCase().includes("grind"))&&F.value===h)):m&&h&&(f=Ye(u.data.product,{Size:m,type:h}))}else d==="kcups"&&O&&oe&&(f=(Me=(n=oe.variants)==null?void 0:n.nodes)==null?void 0:Me.find(z=>z.id===O));if(f&&s)return e.jsx(ze,{onClick:()=>Ae("cart"),lines:[{merchandiseId:f.id,quantity:re,sellingPlanId:s.sellingPlanId}],className:`w-full py-4 px-6 rounded-lg font-semibold text-lg transition-colors duration-200 text-white ${d==="coffee"?"bg-army-600 hover:bg-army-700":"bg-amber-600 hover:bg-amber-700"}`,children:"Start Subscription"});if(d==="coffee"&&v&&s&&!f){const z=m&&h,ge=p&&y&&M,pe=m&&h,T=((Te=u.data)==null?void 0:Te.product)&&(u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box"))&&h;if(z||ge||pe||T)return e.jsxs("div",{className:"text-center py-4 text-red-500 text-sm",children:["Unable to find matching variant. Please try different selections.",e.jsx("br",{}),e.jsxs("span",{className:"text-xs text-gray-500",children:["Product: ",((Oe=(Re=u.data)==null?void 0:Re.product)==null?void 0:Oe.title)||"Unknown"," | Selections: Size=",m,", Type=",h,", Flavors=",p,",",y,",",M]})]})}return d==="coffee"&&v?e.jsxs("div",{className:"text-center py-4 text-gray-500 text-xs",children:["Debug: Product selected but no button.",e.jsx("br",{}),"Product: ",D==null?void 0:D.title,e.jsx("br",{}),"Fetcher data: ",($e=u.data)!=null&&$e.product?"Yes":"No",e.jsx("br",{}),"Selections: Size=",m,", Type=",h,e.jsx("br",{}),"Frequency: ",s==null?void 0:s.label,e.jsx("br",{}),"Variant found: ",f?"Yes":"No"]}):null})()})]}),e.jsx("div",{className:"mt-6",children:e.jsx("a",{href:"/pages/subscriptions",className:"block w-full text-center bg-army-600 hover:bg-army-700 py-3 px-6 rounded-lg font-medium transition-colors duration-200",style:{color:"white"},children:e.jsx("span",{className:"text-white",children:"Learn More About Subscriptions"})})})]})]})})]},t)}return e.jsxs("div",{ref:t==="coffee"?Z:te,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"140px"},children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-army-100 rounded-xl flex items-center justify-center mr-4 text-army-600",children:a.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-4xl font-bold transition-colors duration-200 ${j===t?"text-amber-600":"text-gray-900"}`,children:a.title}),e.jsx("p",{className:"text-lg text-gray-600 mt-1",children:a.description})]})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${j===t?"bg-amber-100 text-amber-800":"bg-army-100 text-army-800"}`,children:t==="subscriptions"?"1 service":`${o.length} ${o.length===1?"item":"items"}`})})]}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${j===t?"bg-amber-600 w-24":"bg-gray-200 w-16"}`})]}),((o==null?void 0:o.length)||0)>0?e.jsx("div",{className:`grid gap-6 ${_==="grid"?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:o.map((d,k)=>{const v=d.id==="gid://shopify/Product/10111589941563"||d.id==="gid://shopify/Product/10111587647803"?{...d,availableForSale:!0}:d;return e.jsx(et,{product:v,loading:k<6?"eager":"lazy",viewMode:_},d.id)})}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6 text-gray-400",children:e.jsx("div",{className:"w-12 h-12",children:a.icon})}),e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:["No ",a.title.toLowerCase()," found"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:((i==null?void 0:i.length)||0)===0?`We don't have any ${a.title.toLowerCase()} products yet.`:"Try adjusting your filters to see more results."}),((i==null?void 0:i.length)||0)>0&&e.jsx("button",{onClick:()=>{const d=new URLSearchParams,k=N.get("section");k&&d.set("section",k),Ee(`?${d.toString()}`)},className:"btn-primary",children:"Clear All Filters"})]})]},t)})})}),e.jsx("div",{className:"bg-army-600 py-20 scroll-fade-in",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center text-white",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("span",{className:"inline-flex items-center text-amber-300 text-sm font-medium mb-4",children:[e.jsx("img",{src:"/coffeeicon.svg",alt:"Coffee",className:"w-5 h-5 mr-2 filter brightness-0 invert"}),"Our Origin Story"]}),e.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Why Nicaragua?"}),e.jsx("div",{className:"w-24 h-1 bg-amber-500 rounded mx-auto mb-8"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8 text-left",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 text-amber-300",children:"Perfect Growing Conditions"}),e.jsx("p",{className:"text-lg leading-relaxed opacity-90",children:"Nicaragua's volcanic soil, high altitude regions, and tropical climate create ideal conditions for coffee cultivation. The mountainous regions provide perfect elevation and temperature variations that allow coffee cherries to develop slowly, concentrating their flavors."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 text-amber-300",children:"Exceptional Flavor Profile"}),e.jsx("p",{className:"text-lg leading-relaxed opacity-90",children:"Nicaraguan coffee is renowned for its well-balanced flavor profile, featuring bright acidity, medium to full body, and complex flavor notes ranging from chocolate and nuts to fruity and floral undertones - perfect for any brewing method."})]})]}),e.jsx("div",{className:"mt-12 p-8 bg-army-700 rounded-xl",children:e.jsx("p",{className:"text-xl leading-relaxed",children:e.jsx("strong",{children:"Every cup of Big River Coffee tells the story of Nicaragua's rich coffee heritage, sustainable farming practices, and the passionate farmers who make it all possible."})})})]})})}),e.jsx("div",{className:"bg-gray-50 py-20 scroll-fade-in",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"bg-amber-600 rounded-xl overflow-hidden shadow-xl",children:e.jsxs("div",{className:"md:flex",children:[e.jsxs("div",{className:"md:w-1/2 p-10 md:p-14 flex flex-col justify-center",children:[e.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"Brewing Guides"}),e.jsx("div",{className:"w-20 h-1 bg-white/30 rounded mb-8"}),e.jsx("p",{className:"text-gray-100 mb-12 text-lg leading-relaxed",children:"Learn how to brew the perfect cup with our detailed guides for various brewing methods. From pour-over to French press, we've got you covered."}),e.jsx("div",{className:"mt-6",children:e.jsx("a",{href:"/pages/brew",className:"bg-army-600 px-8 py-4 rounded-lg hover:bg-army-700 transition-all duration-300 inline-block font-medium",children:e.jsx("span",{className:"text-white",style:{color:"white !important"},children:"View Brewing Guides"})})})]}),e.jsx("div",{className:"md:w-1/2",children:e.jsx("img",{src:"/brewing_3.webp",alt:"Coffee brewing",className:"w-full h-full object-cover"})})]})})})})]})]})," "]})});export{Lt as default,St as meta};
