/**
 * Scroll-triggered animations with Intersection Observer
 * Provides smooth fade-in effects for page sections as they come into view
 */

export function initScrollAnimations() {
  // Check if we're in the browser
  if (typeof window === 'undefined') return;

  // Create intersection observer
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Add visible class to trigger animation
          entry.target.classList.add('visible');
          
          // Optional: Stop observing once animated (for performance)
          // observer.unobserve(entry.target);
        } else {
          // Optional: Remove visible class when out of view (for repeat animations)
          // entry.target.classList.remove('visible');
        }
      });
    },
    {
      // Trigger when 20% of the element is visible
      threshold: 0.2,
      // Start animation 50px before element enters viewport
      rootMargin: '0px 0px -50px 0px'
    }
  );

  // Find all elements with scroll animation classes
  const animatedElements = document.querySelectorAll(
    '.scroll-fade-in, .scroll-slide-left, .scroll-slide-right, .scroll-scale-in'
  );

  // Start observing each element
  animatedElements.forEach((element) => {
    observer.observe(element);
  });

  // Return cleanup function
  return () => {
    observer.disconnect();
  };
}

/**
 * Enhanced button hover effects
 * Adds subtle animations to buttons throughout the site
 */
export function enhanceButtonHovers() {
  if (typeof window === 'undefined') return;

  // Add enhanced hover effects to buttons
  const buttons = document.querySelectorAll('button, .btn, [role="button"]');
  
  buttons.forEach((button) => {
    // Skip if already has custom hover effects
    if (button.classList.contains('no-enhance')) return;

    // Add base transition if not present
    if (!button.style.transition) {
      button.style.transition = 'all 0.3s ease-out';
    }

    // Add hover listeners for additional effects
    button.addEventListener('mouseenter', () => {
      button.style.transform = 'translateY(-1px)';
      button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'none';
    });
  });
}

/**
 * Page transition effects
 * Adds smooth fade-in when page loads
 */
export function initPageTransitions() {
  if (typeof window === 'undefined') return;

  // Add page enter animation
  document.body.classList.add('animate-page-enter');
  
  // Remove animation class after completion
  setTimeout(() => {
    document.body.classList.remove('animate-page-enter');
  }, 600);
}

/**
 * Initialize all animations
 * Call this function when the page loads
 */
export function initAllAnimations() {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      initScrollAnimations();
      enhanceButtonHovers();
      initPageTransitions();
    });
  } else {
    initScrollAnimations();
    enhanceButtonHovers();
    initPageTransitions();
  }
}
