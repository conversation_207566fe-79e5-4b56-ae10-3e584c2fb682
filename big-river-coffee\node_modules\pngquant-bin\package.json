{"name": "pngquant-bin", "version": "9.0.0", "description": "`pngquant` wrapper that makes it seamlessly available as a local dependency", "license": "GPL-3.0+", "repository": "imagemin/pngquant-bin", "type": "module", "exports": "./index.js", "bin": {"pngquant": "cli.js"}, "engines": {"node": ">=18"}, "scripts": {"postinstall": "node lib/install.js", "test": "xo && ava --timeout=120s"}, "files": ["cli.js", "index.js", "lib", "vendor/source"], "keywords": ["imagemin", "compress", "image", "minify", "optimize", "png", "pngquant"], "dependencies": {"bin-build": "^3.0.0", "bin-wrapper": "^4.0.1", "execa": "^8.0.1"}, "devDependencies": {"ava": "^6.1.2", "bin-check": "^4.0.1", "compare-size": "^3.0.0", "tempy": "^3.0.0", "xo": "^0.58.0"}}