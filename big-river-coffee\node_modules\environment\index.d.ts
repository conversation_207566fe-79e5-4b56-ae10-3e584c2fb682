/**
Check if the code is running in a web browser environment.
*/
export const isBrowser: boolean;

/**
Check if the code is running in a [Node.js](https://nodejs.org) environment.
*/
export const isNode: boolean;

/**
Check if the code is running in a [Bun](https://bun.sh) environment.
*/
export const isBun: boolean;

/**
Check if the code is running in a Deno environment.
*/
export const isDeno: boolean;

/**
Check if the code is running in an Electron environment.
*/
export const isElectron: boolean;

/**
Check if the code is running in a [jsdom](https://github.com/jsdom/jsdom) environment.
*/
export const isJsDom: boolean;

/**
Check if the code is running in a [Web Worker](https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API#worker_global_contexts_and_functions) environment, which could be either a dedicated worker, shared worker, or service worker.
*/
export const isWebWorker: boolean;

/**
Check if the code is running in a [Dedicated Worker](https://developer.mozilla.org/en-US/docs/Web/API/DedicatedWorkerGlobalScope) environment.
*/
export const isDedicatedWorker: boolean;

/**
Check if the code is running in a [Shared Worker](https://developer.mozilla.org/en-US/docs/Web/API/SharedWorkerGlobalScope) environment.
*/
export const isSharedWorker: boolean;

/**
Check if the code is running in a [Service Worker](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerGlobalScope) environment.
*/
export const isServiceWorker: boolean;

/**
Check if the code is running on macOS.
*/
export const isMacOs: boolean;

/**
Check if the code is running on Windows.
*/
export const isWindows: boolean;

/**
Check if the code is running on Linux.
*/
export const isLinux: boolean;

/**
Check if the code is running on iOS.
*/
export const isIos: boolean;

/**
Check if the code is running on Android.
*/
export const isAndroid: boolean;
