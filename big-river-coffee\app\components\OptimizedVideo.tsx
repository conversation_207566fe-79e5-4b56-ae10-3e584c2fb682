import { useEffect, useRef, useState } from 'react';

interface OptimizedVideoProps {
  src: string;
  fallbackImage?: string;
  className?: string;
  style?: React.CSSProperties;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  playsInline?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  lazy?: boolean;
  onLoadStart?: () => void;
  onCanPlay?: () => void;
  onError?: () => void;
}

export function OptimizedVideo({
  src,
  fallbackImage,
  className = '',
  style = {},
  autoPlay = true,
  muted = true,
  loop = true,
  playsInline = true,
  preload = 'metadata',
  lazy = true,
  onLoadStart,
  onCanPlay,
  onError,
}: OptimizedVideoProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isInView, setIsInView] = useState(!lazy);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before the video comes into view
        threshold: 0.1,
      }
    );

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // Handle video events
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadStart = () => {
      onLoadStart?.();
    };

    const handleCanPlay = () => {
      setHasLoaded(true);
      onCanPlay?.();
    };

    const handleError = () => {
      setHasError(true);
      onError?.();
    };

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, [onLoadStart, onCanPlay, onError]);

  // Auto-play when video is ready and in view
  useEffect(() => {
    if (hasLoaded && autoPlay && videoRef.current && !isPlaying) {
      const playPromise = videoRef.current.play();
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.warn('Video autoplay failed:', error);
        });
      }
    }
  }, [hasLoaded, autoPlay, isPlaying]);

  // Pause video when out of view to save bandwidth
  useEffect(() => {
    if (!videoRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (videoRef.current) {
            if (entry.isIntersecting) {
              if (autoPlay && hasLoaded) {
                videoRef.current.play().catch(() => {
                  // Ignore autoplay failures
                });
                setIsPlaying(true);
              }
            } else {
              videoRef.current.pause();
              setIsPlaying(false);
            }
          }
        });
      },
      {
        threshold: 0.3, // Pause when less than 30% visible for better performance
        rootMargin: '50px', // Start playing 50px before entering viewport
      }
    );

    observer.observe(videoRef.current);

    return () => observer.disconnect();
  }, [autoPlay, hasLoaded]);

  if (hasError && fallbackImage) {
    return (
      <img
        src={fallbackImage}
        alt="Video fallback"
        className={className}
        style={style}
      />
    );
  }

  return (
    <div className="relative" style={style}>
      {/* Loading placeholder */}
      {!hasLoaded && fallbackImage && (
        <img
          src={fallbackImage}
          alt="Loading..."
          className={`${className} absolute inset-0 w-full h-full object-cover`}
          style={{ objectFit: 'cover' }}
        />
      )}
      
      {/* Video element */}
      {isInView && (
        <video
          ref={videoRef}
          className={`${className} ${!hasLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          autoPlay={autoPlay}
          muted={muted}
          loop={loop}
          playsInline={playsInline}
          preload={preload}
          style={{ objectFit: 'cover' }}
        >
          <source src={src} type="video/mp4" />
          {fallbackImage && (
            <img
              src={fallbackImage}
              alt="Video not supported"
              className="w-full h-full object-cover"
            />
          )}
        </video>
      )}
    </div>
  );
}
