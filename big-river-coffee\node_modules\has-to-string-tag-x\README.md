<a href="https://travis-ci.org/Xotic750/has-to-string-tag-x"
   title="Travis status">
<img
   src="https://travis-ci.org/Xotic750/has-to-string-tag-x.svg?branch=master"
   alt="Travis status" height="18"/>
</a>
<a href="https://david-dm.org/Xotic750/has-to-string-tag-x"
   title="Dependency status">
<img src="https://david-dm.org/Xotic750/has-to-string-tag-x.svg"
   alt="Dependency status" height="18"/>
</a>
<a href="https://david-dm.org/Xotic750/has-to-string-tag-x#info=devDependencies"
   title="devDependency status">
<img src="https://david-dm.org/Xotic750/has-to-string-tag-x/dev-status.svg"
   alt="devDependency status" height="18"/>
</a>
<a href="https://badge.fury.io/js/has-to-string-tag-x" title="npm version">
<img src="https://badge.fury.io/js/has-to-string-tag-x.svg"
   alt="npm version" height="18"/>
</a>
<a name="module_has-to-string-tag-x"></a>

## has-to-string-tag-x
Tests if ES6 @@toStringTag is supported.

**See**: [26.3.1 @@toStringTag](http://www.ecma-international.org/ecma-262/6.0/#sec-@@tostringtag)  
**Version**: 1.4.1  
**Author**: Xotic750 <<EMAIL>>  
**License**: [MIT](&lt;https://opensource.org/licenses/MIT&gt;)  
**Copyright**: Xotic750  
<a name="exp_module_has-to-string-tag-x--module.exports"></a>

### `module.exports` : <code>boolean</code> ⏏
Indicates if `Symbol.toStringTag`exists and is the correct type.
`true`, if it exists and is the correct type, otherwise `false`.

**Kind**: Exported member  
