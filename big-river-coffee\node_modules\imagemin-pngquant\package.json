{"name": "imagemin-pngquant", "version": "10.0.0", "description": "Imagemin plugin for `pngquant`", "license": "MIT", "repository": "imagemin/imagemin-pngquant", "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && npm run test:cover && tsc --noEmit index.d.ts", "test:cover": "c8 --check-coverage --statements 90 ava"}, "files": ["index.js", "index.d.ts"], "keywords": ["compress", "image", "imageminplugin", "minify", "optimize", "png", "pngquant"], "dependencies": {"environment": "^1.0.0", "execa": "^8.0.1", "is-png": "^3.0.1", "ow": "^2.0.0", "pngquant-bin": "^9.0.0", "uint8array-extras": "^1.1.0"}, "devDependencies": {"@types/node": "^20.12.10", "ava": "^6.1.3", "c8": "^9.1.0", "typescript": "^5.4.5", "xo": "^0.58.0"}}