import{j as r}from"./jsx-runtime-6BLhrb1x.js";import{k as j}from"./index-DFyX0ANY.js";function l({connection:i,children:o,resourcesClassName:n}){return r.jsx(j,{connection:i,children:({nodes:a,isLoading:e,PreviousLink:d,NextLink:c})=>{const s=a.map((t,x)=>o({node:t,index:x}));return r.jsxs("div",{children:[r.jsx(d,{children:e?"Loading...":r.jsx("span",{children:"↑ Load previous"})}),n?r.jsx("div",{className:n,children:s}):s,r.jsx(c,{children:e?"Loading...":r.jsx("span",{children:"Load more ↓"})})]})}})}export{l as P};
