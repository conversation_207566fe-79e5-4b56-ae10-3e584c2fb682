{"name": "imagemin", "version": "9.0.1", "description": "Minify images seamlessly", "license": "MIT", "repository": "imagemin/imagemin", "funding": "https://github.com/sponsors/sindresorhus", "type": "module", "exports": "./index.js", "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["minify", "compress", "image", "images", "jpeg", "jpg", "png", "gif", "svg"], "dependencies": {"change-file-extension": "^0.1.1", "environment": "^1.0.0", "file-type": "^19.0.0", "globby": "^14.0.1", "image-dimensions": "^2.3.0", "junk": "^4.0.1", "ow": "^2.0.0", "p-pipe": "^4.0.0", "slash": "^5.1.0", "uint8array-extras": "^1.1.0"}, "devDependencies": {"ava": "^6.1.2", "del": "^7.1.0", "imagemin-jpegtran": "^7.0.0", "imagemin-svgo": "^11.0.1", "imagemin-webp": "^8.0.0", "tempy": "^3.1.0", "xo": "^0.58.0"}}