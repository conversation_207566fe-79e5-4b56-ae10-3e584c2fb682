{"name": "callsites", "version": "4.2.0", "description": "Get callsites from the V8 stack trace API", "license": "MIT", "repository": "sindresorhus/callsites", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}