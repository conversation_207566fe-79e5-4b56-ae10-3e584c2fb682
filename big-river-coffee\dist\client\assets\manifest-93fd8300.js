window.__reactRouterManifest={"entry":{"module":"/assets/entry.client-tBj4aeUI.js","imports":["/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/index-DUgOyoCv.js"],"css":[]},"routes":{"root":{"id":"root","path":"","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":true,"module":"/assets/root-DB6deuoT.js","imports":["/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/index-DUgOyoCv.js","/assets/with-props-CDGKRMwV.js","/assets/index-DFyX0ANY.js","/assets/Aside-Nw97VOZz.js","/assets/CartMain-CDNBADjD.js","/assets/search-DOeYwaXi.js","/assets/Image-CLBTrHWZ.js","/assets/Money-DCyB3BQp.js","/assets/variants-NDcN9fzL.js","/assets/ProductPrice-DtKXlAcT.js"],"css":[]},"routes/[robots.txt]":{"id":"routes/[robots.txt]","parentId":"root","path":"robots.txt","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/_robots.txt_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale)":{"id":"routes/($locale)","parentId":"root","path":":locale?","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs.$blogHandle.$articleHandle":{"id":"routes/($locale).blogs.$blogHandle.$articleHandle","parentId":"routes/($locale)","path":"blogs/:blogHandle/:articleHandle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._blogHandle._articleHandle-MUrvE4IO.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/Image-CLBTrHWZ.js"],"css":[]},"routes/($locale).api.$version.[graphql.json]":{"id":"routes/($locale).api.$version.[graphql.json]","parentId":"routes/($locale)","path":"api/:version/graphql.json","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).api._version._graphql.json_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).sitemap.$type.$page[.xml]":{"id":"routes/($locale).sitemap.$type.$page[.xml]","parentId":"routes/($locale)","path":"sitemap/:type/:page.xml","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).sitemap._type._page_.xml_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs.$blogHandle._index":{"id":"routes/($locale).blogs.$blogHandle._index","parentId":"routes/($locale)","path":"blogs/:blogHandle","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._blogHandle._index-Cx8qxQbN.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/PaginatedResourceSection-gsRrWfOF.js","/assets/Image-CLBTrHWZ.js","/assets/index-DFyX0ANY.js"],"css":[]},"routes/($locale).collections.all-coffees":{"id":"routes/($locale).collections.all-coffees","parentId":"routes/($locale)","path":"collections/all-coffees","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections.all-coffees-BgASxiaV.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).products.roasters-box":{"id":"routes/($locale).products.roasters-box","parentId":"routes/($locale)","path":"products/roasters-box","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).products.roasters-box-sS61-mTA.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/index-DFyX0ANY.js","/assets/Aside-Nw97VOZz.js","/assets/SubscriptionDropdown-BG9q-8LH.js","/assets/getProductOptions-DiFA5z_h.js","/assets/Image-CLBTrHWZ.js","/assets/Money-DCyB3BQp.js","/assets/AddToCartButton-BXvoJOf_.js"],"css":[]},"routes/($locale).collections.$handle":{"id":"routes/($locale).collections.$handle","parentId":"routes/($locale)","path":"collections/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections._handle-Cbx5Ygmh.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/index-DFyX0ANY.js","/assets/ProductCard-BAsJcZLb.js","/assets/variants-NDcN9fzL.js","/assets/AddToCartButton-BXvoJOf_.js","/assets/Image-CLBTrHWZ.js","/assets/Money-DCyB3BQp.js"],"css":[]},"routes/($locale).pages.subscriptions":{"id":"routes/($locale).pages.subscriptions","parentId":"routes/($locale)","path":"pages/subscriptions","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.subscriptions-DkiWZnNa.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).account_.authorize":{"id":"routes/($locale).account_.authorize","parentId":"routes/($locale)","path":"account/authorize","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.authorize-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).collections._index":{"id":"routes/($locale).collections._index","parentId":"routes/($locale)","path":"collections","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections._index-7tlCJriY.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/PaginatedResourceSection-gsRrWfOF.js","/assets/Image-CLBTrHWZ.js","/assets/index-DFyX0ANY.js"],"css":[]},"routes/($locale).account_.register":{"id":"routes/($locale).account_.register","parentId":"routes/($locale)","path":"account/register","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.register-aLn4Xx5z.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).policies.$handle":{"id":"routes/($locale).policies.$handle","parentId":"routes/($locale)","path":"policies/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).policies._handle-CumoAEyI.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).products.$handle":{"id":"routes/($locale).products.$handle","parentId":"routes/($locale)","path":"products/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).products._handle-D8cpC3Sj.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/index-DFyX0ANY.js","/assets/ProductPrice-DtKXlAcT.js","/assets/Aside-Nw97VOZz.js","/assets/SubscriptionDropdown-BG9q-8LH.js","/assets/getProductOptions-DiFA5z_h.js","/assets/Money-DCyB3BQp.js","/assets/AddToCartButton-BXvoJOf_.js"],"css":[]},"routes/($locale).account_.logout":{"id":"routes/($locale).account_.logout","parentId":"routes/($locale)","path":"account/logout","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.logout-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).collections.all":{"id":"routes/($locale).collections.all","parentId":"routes/($locale)","path":"collections/all","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).collections.all-BU8QaIyh.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/ProductCard-BAsJcZLb.js","/assets/AddToCartButton-BXvoJOf_.js","/assets/Aside-Nw97VOZz.js","/assets/scrollAnimations-D2F8dg76.js","/assets/Money-DCyB3BQp.js","/assets/getProductOptions-DiFA5z_h.js","/assets/variants-NDcN9fzL.js","/assets/Image-CLBTrHWZ.js","/assets/index-DFyX0ANY.js"],"css":[]},"routes/($locale).pages.nicaragua":{"id":"routes/($locale).pages.nicaragua","parentId":"routes/($locale)","path":"pages/nicaragua","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.nicaragua-BirWVv9k.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).policies._index":{"id":"routes/($locale).policies._index","parentId":"routes/($locale)","path":"policies","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).policies._index-DjmmcBjm.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).account_.login":{"id":"routes/($locale).account_.login","parentId":"routes/($locale)","path":"account/login","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account_.login-CATraczp.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).discount.$code":{"id":"routes/($locale).discount.$code","parentId":"routes/($locale)","path":"discount/:code","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).discount._code-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).pages.$handle":{"id":"routes/($locale).pages.$handle","parentId":"routes/($locale)","path":"pages/:handle","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages._handle-MPxJi-up.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).[sitemap.xml]":{"id":"routes/($locale).[sitemap.xml]","parentId":"routes/($locale)","path":"sitemap.xml","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._sitemap.xml_-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).blogs._index":{"id":"routes/($locale).blogs._index","parentId":"routes/($locale)","path":"blogs","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).blogs._index-ByHU3LNo.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/PaginatedResourceSection-gsRrWfOF.js","/assets/index-DFyX0ANY.js"],"css":[]},"routes/($locale).pages.brew":{"id":"routes/($locale).pages.brew","parentId":"routes/($locale)","path":"pages/brew","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).pages.brew-uwBnYVgK.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).affiliate":{"id":"routes/($locale).affiliate","parentId":"routes/($locale)","path":"affiliate","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).affiliate-6r3lhiC1.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).our-story":{"id":"routes/($locale).our-story","parentId":"routes/($locale)","path":"our-story","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).our-story-CKJuOaby.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/scrollAnimations-D2F8dg76.js"],"css":[]},"routes/($locale).account":{"id":"routes/($locale).account","parentId":"routes/($locale)","path":"account","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account-C4UGtrZi.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).account.orders._index":{"id":"routes/($locale).account.orders._index","parentId":"routes/($locale).account","path":"orders","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.orders._index-Wgu4xua2.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/PaginatedResourceSection-gsRrWfOF.js","/assets/index-DFyX0ANY.js","/assets/Money-DCyB3BQp.js"],"css":[]},"routes/($locale).account.orders.$id":{"id":"routes/($locale).account.orders.$id","parentId":"routes/($locale).account","path":"orders/:id","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.orders._id-CMNDGeZt.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/Money-DCyB3BQp.js","/assets/Image-CLBTrHWZ.js"],"css":[]},"routes/($locale).account.addresses":{"id":"routes/($locale).account.addresses","parentId":"routes/($locale).account","path":"addresses","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.addresses-BHuVeDIu.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).account.profile":{"id":"routes/($locale).account.profile","parentId":"routes/($locale).account","path":"profile","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account.profile-BBXe8pV4.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).account._index":{"id":"routes/($locale).account._index","parentId":"routes/($locale).account","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account._index-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).account.$":{"id":"routes/($locale).account.$","parentId":"routes/($locale).account","path":"*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).account._-l0sNRNKZ.js","imports":[],"css":[]},"routes/($locale).contact":{"id":"routes/($locale).contact","parentId":"routes/($locale)","path":"contact","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).contact-D3z3xjjG.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).search":{"id":"routes/($locale).search","parentId":"routes/($locale)","path":"search","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).search-BiR11o24.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/index-DFyX0ANY.js","/assets/search-DOeYwaXi.js","/assets/Image-CLBTrHWZ.js","/assets/Money-DCyB3BQp.js"],"css":[]},"routes/($locale)._index":{"id":"routes/($locale)._index","parentId":"routes/($locale)","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._index-CoCdRJ8G.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/index-DUgOyoCv.js"],"css":[]},"routes/($locale).cart":{"id":"routes/($locale).cart","parentId":"routes/($locale)","path":"cart","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).cart-BW4Gr_oG.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/jsx-runtime-6BLhrb1x.js","/assets/chunk-D4RADZKF-Oib9MYUd.js","/assets/CartMain-CDNBADjD.js","/assets/index-DFyX0ANY.js","/assets/Aside-Nw97VOZz.js","/assets/variants-NDcN9fzL.js","/assets/ProductPrice-DtKXlAcT.js","/assets/Money-DCyB3BQp.js","/assets/Image-CLBTrHWZ.js"],"css":[]},"routes/($locale).cart.$lines":{"id":"routes/($locale).cart.$lines","parentId":"routes/($locale).cart","path":":lines","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale).cart._lines-oMwFXp6D.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]},"routes/($locale).$":{"id":"routes/($locale).$","parentId":"routes/($locale)","path":"*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/(_locale)._-CC7uRWrh.js","imports":["/assets/with-props-CDGKRMwV.js","/assets/chunk-D4RADZKF-Oib9MYUd.js"],"css":[]}},"url":"/assets/manifest-93fd8300.js","version":"93fd8300"};