sudo: false
language: node_js
branches:
  only:
    - master
    - /^greenkeeper/.*$/
notifications:
  email: false
node_js:
  - "9.6"
  - "9.5"
  - "9.4"
  - "9.3"
  - "9.2"
  - "9.1"
  - "9.0"
  - "8.9"
  - "8.8"
  - "8.7"
  - "8.6"
  - "8.5"
  - "8.4"
  - "8.3"
  - "8.2"
  - "8.1"
  - "8.0"
  - "7.10"
  - "7.9"
  - "7.8"
  - "7.7"
  - "7.6"
  - "7.5"
  - "7.4"
  - "7.3"
  - "7.2"
  - "7.1"
  - "7.0"
  - "6.11"
  - "6.10"
  - "6.9"
  - "6.8"
  - "6.7"
  - "6.6"
  - "6.5"
  - "6.4"
  - "6.3"
  - "6.2"
  - "6.1"
  - "6.0"
  - "5.12"
  - "5.11"
  - "5.10"
  - "5.9"
  - "5.8"
  - "5.7"
  - "5.6"
  - "5.5"
  - "5.4"
  - "5.3"
  - "5.2"
  - "5.1"
  - "5.0"
  - "4.8"
  - "4.7"
  - "4.6"
  - "4.5"
  - "4.4"
  - "4.3"
  - "4.2"
  - "4.1"
  - "4.0"
  - "iojs-v3.3"
  - "iojs-v3.2"
  - "iojs-v3.1"
  - "iojs-v3.0"
  - "iojs-v2.5"
  - "iojs-v2.4"
  - "iojs-v2.3"
  - "iojs-v2.2"
  - "iojs-v2.1"
  - "iojs-v2.0"
  - "iojs-v1.8"
  - "iojs-v1.7"
  - "iojs-v1.6"
  - "iojs-v1.5"
  - "iojs-v1.4"
  - "iojs-v1.3"
  - "iojs-v1.2"
  - "iojs-v1.1"
  - "iojs-v1.0"
  - "0.12"
  - "0.11"
  - "0.10"
  - "0.9"
  - "0.8"
  - "0.6"
  - "0.4"
before_install:
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ]; then npm install -g npm@1.3 ; elif [ "${TRAVIS_NODE_VERSION}" != "0.9" ]; then case "$(npm --version)" in 1.*) npm install -g npm@1.4.28 ;; 2.*) npm install -g npm@2 ; esac ; fi'
  - 'if [ "${TRAVIS_NODE_VERSION}" != "0.6" ] && [ "${TRAVIS_NODE_VERSION}" != "0.9" ]; then if [ "${TRAVIS_NODE_VERSION%${TRAVIS_NODE_VERSION#[0-9]}}" = "0" ] || [ "${TRAVIS_NODE_VERSION:0:4}" = "iojs" ]; then npm install -g npm@4.5; elif [[ "${TRAVIS_NODE_VERSION%${TRAVIS_NODE_VERSION#[0-9]}}" =~ ^[4-5]+$ ]]; then npm install -g npm@5.3; else npm install -g npm; fi; fi'
install:
  - 'if [ "${TRAVIS_NODE_VERSION}" = "0.6" ]; then nvm install 0.8 && npm install -g npm@1.3 && npm install -g npm@1.4.28 && npm install -g npm@2 && npm install && nvm use --delete-prefix "${TRAVIS_NODE_VERSION}"; else npm install; fi;'
script:
  - 'npm test'
matrix:
  fast_finish: true
  allow_failures:
    - node_js: "0.11"
    - node_js: "0.9"
    - node_js: "0.6"
    - node_js: "0.4"
