{"name": "imagemin-mozjpeg", "version": "10.0.0", "description": "Imagemin plugin for mozjpeg", "license": "MIT", "repository": "imagemin/imagemin-mozjpeg", "type": "module", "exports": "./index.js", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "github.com/shinnn"}], "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["compress", "image", "imageminplugin", "img", "jpeg", "jpg", "minify", "mozjpeg", "optimize"], "dependencies": {"execa": "^6.0.0", "is-jpg": "^3.0.0", "mozjpeg": "^8.0.0"}, "devDependencies": {"ava": "^3.15.0", "is-progressive": "^3.0.0", "xo": "^0.47.0"}}