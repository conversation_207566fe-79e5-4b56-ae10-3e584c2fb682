{"name": "@sindresorhus/is", "version": "6.3.1", "description": "Type check values", "license": "MIT", "repository": "sindresorhus/is", "funding": "https://github.com/sindresorhus/is?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "engines": {"node": ">=16"}, "scripts": {"build": "del dist && tsc", "test": "tsc --noEmit && xo && NODE_OPTIONS='--loader=ts-node/esm --no-warnings=ExperimentalWarning' ava", "prepare": "npm run build"}, "files": ["dist"], "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "devDependencies": {"@sindresorhus/tsconfig": "^4.0.0", "@types/jsdom": "^21.1.1", "@types/node": "^20.5.0", "@types/zen-observable": "^0.8.3", "ava": "^5.3.1", "del-cli": "^5.0.0", "jsdom": "^22.1.0", "rxjs": "^7.8.1", "tempy": "^3.1.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "xo": "^0.56.0", "zen-observable": "^0.10.0", "expect-type": "^0.16.0"}, "ava": {"extensions": {"ts": "module"}}}