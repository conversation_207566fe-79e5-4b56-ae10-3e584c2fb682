import{w as r}from"./with-props-CDGKRMwV.js";import{j as i}from"./jsx-runtime-6BLhrb1x.js";import{u as o,L as t}from"./chunk-D4RADZKF-Oib9MYUd.js";const d=r(function(){const{policies:s}=o();return i.jsxs("div",{className:"policies",children:[i.jsx("h1",{children:"Policies"}),i.jsx("div",{children:s.map(e=>e?i.jsx("fieldset",{children:i.jsx(t,{to:`/policies/${e.handle}`,children:e.title})},e.id):null)})]})});export{d as default};
