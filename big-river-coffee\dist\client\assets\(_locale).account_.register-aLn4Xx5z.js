import{w as s}from"./with-props-CDGKRMwV.js";import{j as e}from"./jsx-runtime-6BLhrb1x.js";import{a,F as r}from"./chunk-D4RADZKF-Oib9MYUd.js";const m=()=>[{title:"Register | Big River Coffee"}],d=s(function(){const t=a().state==="submitting";return e.jsx("div",{className:"min-h-screen",style:{backgroundColor:"#f97316"},children:e.jsx("div",{className:"max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-16",children:e.jsx("div",{className:"bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100",children:e.jsxs("div",{className:"p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("span",{className:"text-white font-semibold text-sm uppercase tracking-wider bg-army-600 px-4 py-1 rounded-full shadow-sm inline-block mb-4",children:"Create Account"}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Join Big River Coffee"}),e.jsx("p",{className:"text-gray-600",children:"Create your account to track orders and save favorites"})]}),e.jsx(r,{method:"post",className:"space-y-4",children:e.jsx("div",{children:e.jsxs("button",{type:"submit",disabled:t,className:"group relative flex w-full justify-center rounded-md bg-army-600 px-4 py-3.5 text-sm font-semibold text-white hover:bg-army-700 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-army-600 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:t?e.jsxs("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsx("svg",{className:"h-5 w-5 text-white group-hover:text-gray-100",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})}),t?"Creating account...":"Create account"]})})}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",e.jsx("a",{href:"/account/login",className:"font-medium text-army-600 hover:text-army-500",children:"Sign in here"})]})})]})})})})});export{d as default,m as meta};
