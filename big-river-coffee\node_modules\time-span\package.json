{"name": "time-span", "version": "5.1.0", "description": "Simplified high resolution timing", "license": "MIT", "repository": "sindresorhus/time-span", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "node": "./index.js", "default": "./browser.js"}, "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "browser.js", "index.d.ts"], "keywords": ["time", "span", "elapsed", "process", "hrtime", "highres", "timing", "perf", "performance", "bench", "benchmark", "profiling", "measure", "seconds", "milliseconds", "nanoseconds"], "dependencies": {"convert-hrtime": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "delay": "^5.0.0", "in-range": "^3.0.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}