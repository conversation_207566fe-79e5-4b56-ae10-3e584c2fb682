import { Predicate, type PredicateOptions } from './predicate.js';
export declare class DataViewPredicate extends Predicate<DataView> {
    /**
    @hidden
    */
    constructor(options?: PredicateOptions);
    /**
    Test a DataView to have a specific byte length.

    @param byteLength - The byte length of the DataView.
    */
    byteLength(byteLength: number): this;
    /**
    Test a DataView to have a minimum byte length.

    @param byteLength - The minimum byte length of the DataView.
    */
    minByteLength(byteLength: number): this;
    /**
    Test a DataView to have a minimum byte length.

    @param length - The minimum byte length of the DataView.
    */
    maxByteLength(byteLength: number): this;
}
